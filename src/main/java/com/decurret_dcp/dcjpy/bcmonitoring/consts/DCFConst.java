package com.decurret_dcp.dcjpy.bcmonitoring.consts;

public class DCFConst {

  // Format date time
  public static final String DATETIME_FORMAT_API = "yyyy-MM-dd'T'HH:mm:ss";
  public static final String TIMEZONE_API = "Asia/Tokyo";
  public static final String TIMEZONE_DB = "GMT";

  // Environment
  public static final String PROD = "prod";
  public static final String LOCAL = "local";
  public static final String TEST = "test";

  // Specific characters
  public static final String SLASH = "/";
  public static final String DOT = ".";
  public static final String EMPTY = "";

  // Constants

  public static final String UINT = "uint";
  public static final String ADDRESS = "address";
  public static final String STRING = "string";
  public static final String BOOL = "bool";
  public static final String BYTES = "bytes";
  public static final String BYTES_32 = "bytes32";
}
